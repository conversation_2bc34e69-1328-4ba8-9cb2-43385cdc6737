<template>
    <div class="app-footer">
        <van-tabbar
            v-model="active"
            @change="onTabChange"
            fixed
            placeholder
            safe-area-inset-bottom
            active-color="#2563eb"
            inactive-color="#64748b"
        >
            <van-tabbar-item
                v-for="(item, index) in tabItems"
                :key="index"
                :icon="item.icon"
                :to="item.path"
                :badge="item.badge"
            >
                {{ item.text }}
            </van-tabbar-item>
        </van-tabbar>
    </div>
</template>

<script>
export default {
    name: 'AppFooter',
    data() {
        return {
            active: 0,
            tabItems: [
                {
                    text: '首页',
                    icon: 'home-o',
                    path: '/home',
                    badge: ''
                },
                {
                    text: '课程',
                    icon: 'play-circle-o',
                    path: '/courses',
                    badge: ''
                },
                {
                    text: '学习',
                    icon: 'comment-o',
                    path: '/study',
                    badge: ''
                },
                {
                    text: '我的',
                    icon: 'user-o',
                    path: '/profile',
                    badge: ''
                }
            ]
        }
    },
    watch: {
        $route(to) {
            // 根据当前路由设置active状态
            this.setActiveByRoute(to.path)
        }
    },
    mounted() {
        // 初始化时设置active状态
        this.setActiveByRoute(this.$route.path)
    },
    methods: {
        onTabChange(index) {
            // 可以在这里添加切换标签时的逻辑
            console.log('Tab changed to:', this.tabItems[index].text)
        },
        setActiveByRoute(path) {
            // 根据路由路径设置active状态
            const index = this.tabItems.findIndex(item => path.includes(item.path))
            if (index !== -1) {
                this.active = index
            }
        },
        // 设置徽章数量
        setBadge(tabIndex, badge) {
            if (this.tabItems[tabIndex]) {
                this.tabItems[tabIndex].badge = badge
            }
        }
    }
}
</script>

<style lang="scss" scoped>
// 蓝白主题色彩变量
$primary-blue: #2563eb;
$light-blue: #3b82f6;
$text-secondary: #64748b;
$text-light: #94a3b8;

.app-footer {
    ::v-deep .van-tabbar {
        height: 60px;
        padding: 10px;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-top: 1px solid rgba(37, 99, 235, 0.08);
        box-shadow: 0 -4px 20px rgba(37, 99, 235, 0.06);
        backdrop-filter: blur(10px);
    }

    ::v-deep .van-tabbar-item {
        color: $text-secondary;
        transition: all 0.3s ease;

        &--active {
            color: $primary-blue;

            .van-tabbar-item__icon {
                transform: scale(1.1);
            }

            .van-tabbar-item__text {
                font-weight: 600;
            }
        }

        &:active {
            transform: scale(0.95);
        }
    }

    ::v-deep .van-tabbar-item__text {
        font-size: 12px;
        margin-top: 6px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    ::v-deep .van-tabbar-item__icon {
        font-size: 24px;
        margin-bottom: 2px;
        transition: all 0.3s ease;
    }
}
</style>
