// eslint-disable-next-line prettier/prettier
import { getCourseDetail, getLearningRecords, getLessonDetail, getMyCourses, getNote, getStudyCenterData, getStudyProgress, saveNote, syncStudyProgress } from '@/api/study'


const state = {
    progress: {},
    learningRecords: [],
    currentModule: null,
    studyCenterData: {},
    myCourses: [],
    currentLesson: {},
    currentCourse: {},
    noteContent: ''
}

const getters = {
    studyProgress: state => state.progress,
    recentLearning: state => state.learningRecords,
    studyCenterData: state => state.studyCenterData,
    myCourses: state => state.myCourses,
    currentLesson: state => state.currentLesson,
    currentCourse: state => state.currentCourse,
    noteContent: state => state.noteContent
}

const mutations = {
    SET_PROGRESS(state, data) {
        state.progress = data
    },
    SET_LEARNING_RECORDS(state, data) {
        state.learningRecords = data
    },
    SET_CURRENT_MODULE(state, module) {
        state.currentModule = module
    },
    SET_STUDY_CENTER_DATA(state, data) {
        state.studyCenterData = data
    },
    SET_MY_COURSES(state, data) {
        state.myCourses = data
    },
    SET_CURRENT_LESSON(state, data) {
        state.currentLesson = data
    },
    SET_CURRENT_COURSE(state, data) {
        state.currentCourse = data
    },
    SET_NOTE_CONTENT(state, content) {
        state.noteContent = content
    }
}

const actions = {
    // 获取学习中心数据
    async getStudyCenterData({ commit }, userId) {
        try {
            commit('SET_LOADING', true)
            const data = await getStudyCenterData({ userId })
            commit('SET_STUDY_CENTER_DATA', data)
            return data
        } catch (error) {
            console.error('Get study center data failed:', error)
            throw error
        } finally {
            commit('SET_LOADING', false)
        }
    },

    // 获取我的课程列表
    async getMyCourses({ commit }, params) {
        try {
            commit('SET_LOADING', true)
            const data = await getMyCourses(params)
            commit('SET_MY_COURSES', data)
            return data
        } catch (error) {
            console.error('Get my courses failed:', error)
            throw error
        } finally {
            commit('SET_LOADING', false)
        }
    },

    // 获取课时详情
    async getLessonDetail({ commit }, lessonId) {
        try {
            commit('SET_LOADING', true)
            const data = await getLessonDetail({ lessonId })
            commit('SET_CURRENT_LESSON', data)
            return data
        } catch (error) {
            console.error('Get lesson detail failed:', error)
            throw error
        } finally {
            commit('SET_LOADING', false)
        }
    },

    // 获取课程详情
    async getCourseDetail({ commit }, courseId) {
        try {
            commit('SET_LOADING', true)
            const data = await getCourseDetail({ courseId })
            commit('SET_CURRENT_COURSE', data)
            return data
        } catch (error) {
            console.error('Get course detail failed:', error)
            throw error
        } finally {
            commit('SET_LOADING', false)
        }
    },

    // 保存笔记
    async saveNote({ commit }, data) {
        try {
            commit('SET_LOADING', true)
            await saveNote(data)
            commit('SET_NOTE_CONTENT', data.content)
            return true
        } catch (error) {
            console.error('Save note failed:', error)
            throw error
        } finally {
            commit('SET_LOADING', false)
        }
    },

    // 获取笔记
    async getNote({ commit }, lessonId) {
        try {
            commit('SET_LOADING', true)
            const data = await getNote({ lessonId })
            commit('SET_NOTE_CONTENT', data.content)
            return data
        } catch (error) {
            console.error('Get note failed:', error)
            throw error
        } finally {
            commit('SET_LOADING', false)
        }
    },

    // 获取学习进度
    async getStudyProgress({ commit }, courseId) {
        try {
            commit('SET_LOADING', true)
            const data = await getStudyProgress(courseId)
            commit('SET_PROGRESS', data)
            return data
        } catch (error) {
            console.error('Get study progress failed:', error)
            throw error
        } finally {
            commit('SET_LOADING', false)
        }
    },

    // 同步学习进度
    async syncStudyProgress({ commit }, params) {
        try {
            commit('SET_LOADING', true)
            const data = await syncStudyProgress(params)
            // 更新进度
            commit('SET_PROGRESS', data)
            return data
        } catch (error) {
            console.error('Sync study progress failed:', error)
            throw error
        } finally {
            commit('SET_LOADING', false)
        }
    },

    // 获取学习记录
    async getLearningRecords({ commit }, params) {
        try {
            commit('SET_LOADING', true)
            const data = await getLearningRecords(params)
            commit('SET_LEARNING_RECORDS', data)
            return data
        } catch (error) {
            console.error('Get learning records failed:', error)
            throw error
        } finally {
            commit('SET_LOADING', false)
        }
    }
}

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions
}
