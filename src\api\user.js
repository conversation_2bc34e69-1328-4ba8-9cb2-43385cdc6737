import { axiosInstance } from '@/boot/main/axios'

// 登录
export function login(data) {
    return axiosInstance({
        url: '/api/login/loginpassword',
        method: 'post',
        data: {
            PhoneNumber: data.username,
            Password: data.password
        }
    })
}
// 手机号验证登录
export function loginsms(data) {
    return axiosInstance({
        url: '/api/login/loginsms',
        method: 'post',
        data: {
            PhoneNumber: data.phone,
            VerificationCode: data.code
        }
    })
}

// 注销
export function logout() {
    return axiosInstance({
        url: '/api/user/logout',
        method: 'post',
        data: {
            AppID: process.env.VUE_APP_ID || 'medical-training-system'
        }
    })
}

// 获取用户信息
export function getUserInfo() {
    return axiosInstance({
        url: '/api/user/info',
        method: 'get',
        params: {
            AppID: process.env.VUE_APP_ID || 'medical-training-system'
        }
    })
}

// 获取验证码
export function getVerificationCode(phone) {
    return axiosInstance({
        url: '/api/otms/sms/callexternalsmsservice',
        method: 'post',
        data: {
            PhoneNumber: phone
        }
    })
}
// 验证验证码
export function verifyVerificationCode(data) {
    return axiosInstance({
        url: '/api/otms/sms/verifycode',
        method: 'post',
        data
    })
}

// 注册
export function register(data) {
    return axiosInstance({
        url: 'api/otms/base/user/register',
        method: 'post',
        data
    })
}
