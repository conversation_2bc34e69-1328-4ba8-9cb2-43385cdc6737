import axios from 'axios'
import { Toast } from 'vant'

// 错误消息映射
const ERROR_MESSAGES = {
    400: '请求参数错误',
    401: '登录已过期，请重新登录',
    403: '没有权限访问',
    404: '请求的资源不存在',
    408: '请求超时',
    500: '服务器内部错误',
    502: '网关错误',
    503: '服务暂不可用',
    504: '网关超时',
    default: '网络连接异常，请检查网络后重试'
}

// 创建axios实例
const axiosInstance = axios.create({
    baseURL: process.env.NODE_ENV === 'development' ? '' : process.env.VUE_APP_API_URL,
    timeout: 50000, // 请求超时时间
    withCredentials: true // 允许携带cookie
})

// 请求拦截器
axiosInstance.interceptors.request.use(
    config => {
        // 从localStorage中获取token
        const token = localStorage.getItem('token')
        // 如果token存在，则添加到请求头中
        if (token) {
            config.headers.Authorization = `Bearer ${token}`
        }

        // 添加AppID
        config.headers['X-App-ID'] = process.env.VUE_APP_ID || 'medical-training-system'

        return config
    },
    error => {
        // 处理请求错误
        console.error('Request error:', error)
        Toast.fail('请求发送失败')
        return Promise.reject(error)
    }
)

// 响应拦截器
axiosInstance.interceptors.response.use(
    response => {
        // 处理响应数据

        return response.data
    },
    error => {
        // 处理HTTP错误
        console.error('HTTP error:', error)

        let errorMessage

        if (error.response) {
            // 服务器返回了错误状态码
            const status = error.response.status
            errorMessage = error.response.data.Data

            // 特殊处理401错误
            if (status === 401) {
                // 清除本地存储的认证信息
                localStorage.removeItem('token')
                localStorage.removeItem('userInfo')

                // 延迟跳转到登录页，避免在某些页面中出现路由错误
                setTimeout(() => {
                    if (window.location.pathname !== '/login') {
                        window.location.href = '/login'
                    }
                }, 1500)
            }
        } else if (error.request) {
            // 请求已发出但没有收到响应
            errorMessage = '网络连接超时，请检查网络后重试'
        } else {
            // 其他错误
            errorMessage = error.message || ERROR_MESSAGES.default
        }

        // 显示错误提示
        Toast(errorMessage)

        // 返回一个包含错误信息的对象，而不是抛出错误
        return Promise.reject(error)
    }
)

export { axiosInstance }
// eslint-disable-next-line prettier/prettier

