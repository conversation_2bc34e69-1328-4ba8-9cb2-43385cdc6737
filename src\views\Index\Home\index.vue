<template>
    <div class="home-page">
        <van-nav-bar title="首页" fixed />
        <div class="content">
            <!-- 搜索栏 -->
            <div class="search-section">
                <van-search
                    v-model="searchValue"
                    placeholder="搜索课程、讲师、专业..."
                    @search="onSearch"
                    @clear="onClear"
                    shape="round"
                    background="#f7f8fa"
                />
            </div>

            <!-- 轮播图 -->
            <div class="banner-section">
                <van-swipe :autoplay="3000" indicator-color="white" class="banner-swipe">
                    <van-swipe-item v-for="banner in banners" :key="banner.id">
                        <div class="banner-item" @click="goToBanner(banner)">
                            <img :src="banner.image" :alt="banner.title" />
                            <div class="banner-overlay">
                                <div class="banner-title">{{ banner.title }}</div>
                                <div class="banner-subtitle">{{ banner.subtitle }}</div>
                            </div>
                        </div>
                    </van-swipe-item>
                </van-swipe>
            </div>

            <!-- 快捷入口 -->
            <div class="quick-entry">
                <van-grid :column-num="4" :border="false" class="entry-grid">
                    <van-grid-item
                        v-for="entry in quickEntries"
                        :key="entry.id"
                        @click="goToEntry(entry)"
                        class="entry-item"
                    >
                        <div class="entry-content">
                            <van-icon :name="entry.icon" class="entry-icon" />
                            <span class="entry-text">{{ entry.text }}</span>
                        </div>
                    </van-grid-item>
                </van-grid>
            </div>

            <!-- 分类课程菜单 -->
            <div class="category-menu">
                <div class="section-header">
                    <h3>课程分类</h3>
                    <span class="more-btn" @click="goToAllCategories">查看全部</span>
                </div>
                <van-grid :column-num="2" :border="false" class="category-grid">
                    <van-grid-item
                        v-for="category in categories"
                        :key="category.ID"
                        @click="goToCategory(category)"
                        class="category-item"
                    >
                        <div class="category-content">
                            <div class="category-image" v-if="category.CoverImage">
                                <van-image :src="category.CoverImage" fit="cover" />
                            </div>
                            <div class="category-icon" v-else>
                                <van-icon :name="getCategoryIcon(category.CategoryName)" />
                            </div>
                            <div class="category-info">
                                <div class="category-name">{{ category.CategoryName }}</div>
                                <div class="category-count">{{ category.Num }}门课程</div>
                                <div class="category-desc" v-if="category.CategoryDesc">
                                    {{ category.CategoryDesc }}
                                </div>
                            </div>
                        </div>
                    </van-grid-item>
                </van-grid>
            </div>

            <!-- 推荐课程 -->
            <div class="recommend-section">
                <div class="section-header">
                    <h3>推荐课程</h3>
                    <span class="more-btn" @click="goToAllCourses">更多课程</span>
                </div>
                <div class="course-list">
                    <div
                        v-for="course in recommendCourses"
                        :key="course.ID"
                        class="course-card"
                        @click="goToCourse(course)"
                    >
                        <div class="course-image">
                            <van-image :src="course.CoverImage || course.ListImage" fit="cover" />
                            <div class="course-tags">
                                <van-tag v-if="course.IsHot" type="warning" size="mini"
                                    >热门</van-tag
                                >
                                <van-tag :type="getLevelColor(course.Level)" size="mini">{{
                                    getLevelText(course.Level)
                                }}</van-tag>
                            </div>
                            <div class="course-duration">
                                <van-icon name="clock-o" />
                                {{ formatDuration(course.TotalDuration) }}
                            </div>
                        </div>
                        <div class="course-info">
                            <div class="course-title">{{ course.CourseName }}</div>
                            <div class="course-desc">{{ course.CourseDesc }}</div>
                            <div class="course-teacher" v-if="course.DocentName">
                                <van-icon name="manager-o" />
                                {{ course.DocentName }}
                            </div>
                            <div class="course-meta">
                                <div class="course-lessons">
                                    <van-icon name="play-circle-o" />
                                    {{ course.TotalLessons }}个课时
                                </div>
                                <div class="course-requirements" v-if="course.FinishRequirements">
                                    <van-icon name="certificate" />
                                    {{ course.FinishRequirements }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 学习动态 -->
            <div class="activity-section">
                <div class="section-header">
                    <h3>学习动态</h3>
                </div>
                <van-list class="activity-list">
                    <van-cell
                        v-for="activity in activities"
                        :key="activity.id"
                        :title="activity.title"
                        :label="activity.time"
                        :value="activity.type"
                        class="activity-item"
                    >
                        <template #icon>
                            <van-image round width="40" height="40" :src="activity.avatar" />
                        </template>
                    </van-cell>
                </van-list>
            </div>
        </div>
    </div>
</template>

<script>
import { getCourseCategories, getCourseList } from '@/api/course'
export default {
    name: 'HomePage',
    data() {
        return {
            searchValue: '',
            banners: [
                {
                    id: 1,
                    title: '新课程上线',
                    subtitle: '人体解剖学基础课程全新发布',
                    image: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    link: '/courses'
                },
                {
                    id: 2,
                    title: '免费学习周',
                    subtitle: '精选课程限时免费开放',
                    image: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    link: '/courses'
                },
                {
                    id: 3,
                    title: '专业认证',
                    subtitle: '完成学习获得权威认证证书',
                    image: 'https://img.yzcdn.cn/vant/cat.jpeg',
                    link: '/certificates'
                }
            ],
            quickEntries: [
                { id: 1, icon: 'play-circle-o', text: '课程学习', path: '/courses' },
                { id: 2, icon: 'certificate', text: '考试认证', path: '/exams' },
                { id: 3, icon: 'records', text: '学习记录', path: '/study' },
                { id: 4, icon: 'question-o', text: '在线答疑', path: '/qa' }
            ],
            categories: [],
            recommendCourses: [],
            activities: [
                {
                    id: 1,
                    title: '张医生完成了《人体解剖学基础》',
                    time: '2小时前',
                    type: '课程完成',
                    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg'
                },
                {
                    id: 2,
                    title: '李护士获得了护理学认证证书',
                    time: '5小时前',
                    type: '证书获得',
                    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg'
                },
                {
                    id: 3,
                    title: '王医生开始学习《内科学精讲》',
                    time: '1天前',
                    type: '开始学习',
                    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg'
                }
            ]
        }
    },
    created() {
        this.loadCourseList()
        this.loadCourseCategories()
    },
    methods: {
        async loadCourseList() {
            const res = await getCourseList({ PageSize: 3 })
            console.log('推荐课程:', res)

            this.recommendCourses = res.Data.PageList || []
        },
        // 获取课程分类
        async loadCourseCategories() {
            try {
                const res = await getCourseCategories()
                console.log('课程分类:', res)

                if (res && res.Data && res.Data.PageList) {
                    this.categories = res.Data.PageList
                } else if (res && res.Data) {
                    // 如果直接返回数组
                    this.categories = Array.isArray(res.Data) ? res.Data : []
                } else {
                    this.categories = []
                }
            } catch (error) {
                console.error('获取课程分类失败:', error)
                this.categories = []
            }
        },

        onSearch(value) {
            console.log('搜索:', value)
            // 跳转到搜索结果页
            this.$router.push(`/search?q=${encodeURIComponent(value)}`)
        },
        onClear() {
            this.searchValue = ''
        },
        goToBanner(banner) {
            console.log('点击轮播图:', banner.title)
            this.$router.push(banner.link)
        },
        goToEntry(entry) {
            console.log('点击快捷入口:', entry.text)
            this.$router.push(entry.path)
        },
        goToAllCategories() {
            console.log('查看全部分类')
            this.$router.push('/categories')
        },
        goToCategory(category) {
            console.log('点击分类:', category.CategoryName)
            this.$router.push(`/courses?category=${category.ID}&categoryId=${category.CategoryID}`)
        },
        goToAllCourses() {
            console.log('查看更多课程')
            this.$router.push('/courses')
        },
        goToCourse(course) {
            console.log('点击课程:', course.CourseName || course.title)
            this.$router.push(`/course-detail/${course.ID || course.id}`)
        },
        getPriceText(price) {
            return price === 0 ? '免费' : `¥${price}`
        },
        getLevelText(level) {
            const levelMap = {
                1: '初级',
                2: '中级',
                3: '高级',
                4: '专家级'
            }
            return levelMap[level] || '初级'
        },
        getLevelColor(level) {
            const colorMap = {
                1: 'success', // 绿色
                2: 'primary', // 蓝色
                3: 'danger', // 红色
                4: 'warning' // 橙色（专家级）
            }
            return colorMap[level] || 'success'
        },
        formatDuration(seconds) {
            if (!seconds) return '0分钟'
            const hours = Math.floor(seconds / 3600)
            const minutes = Math.floor((seconds % 3600) / 60)

            if (hours > 0) {
                return `${hours}小时${minutes > 0 ? minutes + '分钟' : ''}`
            }
            return `${minutes}分钟`
        },
        // 根据分类名称获取对应图标
        getCategoryIcon(categoryName) {
            const iconMap = {
                基础医学: 'medical-o',
                临床医学: 'hospital-o',
                护理学: 'user-circle-o',
                药学: 'bag-o',
                心血管疾病介入诊疗技术: 'heart-o',
                神经系统疾病: 'brain-o',
                消化系统疾病: 'stomach-o',
                呼吸系统疾病: 'lungs-o',
                内分泌系统疾病: 'gland-o',
                肿瘤学: 'warning-o',
                急诊医学: 'emergency-o',
                康复医学: 'recovery-o',
                预防医学: 'shield-o',
                医学影像: 'scan-o',
                检验医学: 'test-tube-o',
                麻醉学: 'sleep-o',
                外科学: 'scalpel-o',
                妇产科学: 'female-o',
                儿科学: 'baby-o',
                眼科学: 'eye-o',
                耳鼻喉科学: 'ear-o',
                口腔医学: 'tooth-o',
                皮肤科学: 'skin-o',
                精神医学: 'mind-o',
                中医学: 'herb-o',
                医疗设备: 'device-o'
            }

            // 如果找到对应的图标，返回图标名称
            if (iconMap[categoryName]) {
                return iconMap[categoryName]
            }

            // 根据关键词匹配
            if (categoryName.includes('心血管') || categoryName.includes('心脏')) {
                return 'heart-o'
            } else if (categoryName.includes('神经') || categoryName.includes('脑')) {
                return 'brain-o'
            } else if (categoryName.includes('消化') || categoryName.includes('胃肠')) {
                return 'stomach-o'
            } else if (categoryName.includes('呼吸') || categoryName.includes('肺')) {
                return 'lungs-o'
            } else if (categoryName.includes('肿瘤') || categoryName.includes('癌症')) {
                return 'warning-o'
            } else if (categoryName.includes('急诊') || categoryName.includes('急救')) {
                return 'emergency-o'
            } else if (categoryName.includes('康复') || categoryName.includes('理疗')) {
                return 'recovery-o'
            } else if (categoryName.includes('预防') || categoryName.includes('保健')) {
                return 'shield-o'
            } else if (categoryName.includes('影像') || categoryName.includes('放射')) {
                return 'scan-o'
            } else if (categoryName.includes('检验') || categoryName.includes('化验')) {
                return 'test-tube-o'
            } else if (categoryName.includes('麻醉')) {
                return 'sleep-o'
            } else if (categoryName.includes('外科') || categoryName.includes('手术')) {
                return 'scalpel-o'
            } else if (categoryName.includes('妇产') || categoryName.includes('产科')) {
                return 'female-o'
            } else if (categoryName.includes('儿科') || categoryName.includes('小儿')) {
                return 'baby-o'
            } else if (categoryName.includes('眼科') || categoryName.includes('眼')) {
                return 'eye-o'
            } else if (categoryName.includes('耳鼻喉') || categoryName.includes('五官')) {
                return 'ear-o'
            } else if (categoryName.includes('口腔') || categoryName.includes('牙科')) {
                return 'tooth-o'
            } else if (categoryName.includes('皮肤') || categoryName.includes('美容')) {
                return 'skin-o'
            } else if (categoryName.includes('精神') || categoryName.includes('心理')) {
                return 'mind-o'
            } else if (categoryName.includes('中医') || categoryName.includes('中药')) {
                return 'herb-o'
            } else if (categoryName.includes('设备') || categoryName.includes('器械')) {
                return 'device-o'
            } else if (categoryName.includes('护理')) {
                return 'user-circle-o'
            } else if (categoryName.includes('药学') || categoryName.includes('药物')) {
                return 'bag-o'
            } else if (categoryName.includes('临床')) {
                return 'hospital-o'
            } else if (categoryName.includes('基础')) {
                return 'medical-o'
            }

            // 默认图标
            return 'medical-o'
        }
    }
}
</script>

<style lang="scss" scoped>
// 蓝白主题色彩变量
$primary-blue: #2563eb;
$light-blue: #3b82f6;
$lighter-blue: #60a5fa;
$lightest-blue: #dbeafe;
$background-blue: #f8fafc;
$white: #ffffff;
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-light: #94a3b8;

.home-page {
    background: #f5f6f7;
    min-height: 100vh;
    overflow-x: hidden;

    // 隐藏滚动条
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
        display: none;
    }

    .content {
        padding-top: 20px;

        // 搜索栏样式
        .search-section {
            padding: 20px 0px 10px 0px;

            margin-bottom: 0;

            ::v-deep .van-search {
                padding: 10px 0;
                .van-search__content {
                    border: 1px solid #2563eb !important;
                    border-radius: 10px !important;
                }

                .van-search__field {
                    background: transparent;
                }

                input {
                    color: $text-primary;

                    &::placeholder {
                        color: $text-secondary;
                    }
                }
            }
        }

        // 轮播图样式
        .banner-section {
            margin-bottom: 14px;

            .banner-swipe {
                height: 200px;
                border-radius: 16px;
                overflow: hidden;
                margin: 0 16px;
                box-shadow: 0 8px 32px rgba(37, 99, 235, 0.12);

                .banner-item {
                    position: relative;
                    height: 100%;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }

                    .banner-overlay {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        background: linear-gradient(transparent, rgba(30, 41, 59, 0.8));
                        padding: 24px;
                        color: white;

                        .banner-title {
                            font-size: 20px;
                            font-weight: 700;
                            margin-bottom: 6px;
                            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                        }

                        .banner-subtitle {
                            font-size: 14px;
                            opacity: 0.95;
                            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                        }
                    }
                }
            }
        }

        // 快捷入口样式
        .quick-entry {
            background: linear-gradient(135deg, $white 0%, #f1f5f9 100%);
            margin: 0 16px 24px;
            border-radius: 16px;
            padding: 14px 0;
            box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08);
            border: 1px solid rgba(37, 99, 235, 0.05);

            .entry-grid {
                ::v-deep .van-grid-item__content {
                    padding: 10px 8px;
                    transition: all 0.3s ease;

                    &:active {
                        transform: scale(0.95);
                    }
                }
            }

            .entry-content {
                display: flex;
                flex-direction: column;
                align-items: center;

                .entry-icon {
                    font-size: 32px;
                    color: $primary-blue;
                    margin-bottom: 10px;
                    padding: 12px;
                    background: linear-gradient(
                        135deg,
                        $lightest-blue 0%,
                        rgba(219, 234, 254, 0.5) 100%
                    );
                    border-radius: 12px;
                    transition: all 0.3s ease;
                }

                .entry-text {
                    font-size: 13px;
                    color: $text-primary;
                    font-weight: 600;
                }
            }
        }

        // 分类菜单样式
        .category-menu {
            margin: 0 16px 24px;

            .category-grid {
                background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
                border-radius: 16px;
                overflow: hidden;
                box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08);
                border: 1px solid rgba(37, 99, 235, 0.05);

                .category-item {
                    flex-basis: 100% !important;
                    ::v-deep .van-grid-item__content {
                        padding: 20px 16px;
                        transition: all 0.3s ease;

                        &:active {
                            transform: scale(0.98);
                            background-color: rgba(37, 99, 235, 0.02);
                        }
                    }

                    .category-content {
                        display: flex;
                        align-items: center;
                        height: 100%;
                        width: 100%;
                        gap: 12px;

                        .category-image {
                            width: 56px;
                            height: 56px;
                            border-radius: 16px;
                            overflow: hidden;
                            flex-shrink: 0;
                            box-shadow: 0 4px 16px rgba(37, 99, 235, 0.2);

                            ::v-deep .van-image {
                                width: 100%;
                                height: 100%;
                            }
                        }

                        .category-icon {
                            width: 56px;
                            height: 56px;
                            background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                            border-radius: 16px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            flex-shrink: 0;
                            box-shadow: 0 4px 16px rgba(37, 99, 235, 0.2);

                            .van-icon {
                                font-size: 26px;
                                color: white;
                            }
                        }

                        .category-info {
                            flex: 1;
                            min-width: 0; // 防止文本溢出

                            .category-name {
                                font-size: 15px;
                                font-weight: 700;
                                color: $text-primary;
                                margin-bottom: 4px;
                                line-height: 1.3;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            }

                            .category-count {
                                font-size: 11px;
                                color: $text-secondary;
                                font-weight: 500;
                                margin-bottom: 4px;
                            }

                            .category-desc {
                                font-size: 10px;
                                color: $text-light;
                                line-height: 1.3;
                                display: -webkit-box;
                                -webkit-line-clamp: 2;
                                line-clamp: 2;
                                -webkit-box-orient: vertical;
                                overflow: hidden;
                                text-overflow: ellipsis;
                            }
                        }
                    }
                }
            }
        }

        // 推荐课程样式
        .recommend-section {
            margin: 0 16px 24px;

            .course-list {
                display: grid;
                grid-template-columns: repeat(1, 1fr);
                gap: 16px;

                .course-card {
                    background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
                    border-radius: 16px;
                    overflow: hidden;
                    box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08);
                    border: 1px solid rgba(37, 99, 235, 0.05);
                    transition: all 0.3s ease;

                    &:active {
                        transform: scale(0.96);
                        box-shadow: 0 8px 32px rgba(37, 99, 235, 0.15);
                    }

                    .course-image {
                        position: relative;
                        height: 200px;

                        ::v-deep .van-image {
                            width: 100%;
                            height: 100%;
                        }

                        .course-tags {
                            position: absolute;
                            top: 12px;
                            left: 12px;
                            display: flex;
                            gap: 8px;

                            ::v-deep .van-tag {
                                font-weight: 600;
                                border-radius: 8px;
                                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                                backdrop-filter: blur(4px);
                            }
                        }

                        .course-duration {
                            position: absolute;
                            bottom: 12px;
                            right: 12px;
                            background: rgba(0, 0, 0, 0.7);
                            color: white;
                            padding: 6px 10px;
                            border-radius: 12px;
                            font-size: 12px;
                            font-weight: 600;
                            display: flex;
                            align-items: center;
                            gap: 4px;
                            backdrop-filter: blur(4px);

                            .van-icon {
                                font-size: 12px;
                            }
                        }
                    }

                    .course-info {
                        padding: 18px;

                        .course-title {
                            font-size: 16px;
                            font-weight: 700;
                            color: $text-primary;
                            margin-bottom: 8px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            line-height: 1.4;
                        }

                        .course-desc {
                            font-size: 13px;
                            color: $text-secondary;
                            margin-bottom: 12px;
                            line-height: 1.5;
                            display: -webkit-box;
                            -webkit-line-clamp: 2;
                            line-clamp: 2;
                            -webkit-box-orient: vertical;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        .course-teacher {
                            font-size: 13px;
                            color: $primary-blue;
                            margin-bottom: 12px;
                            font-weight: 600;
                            display: flex;
                            align-items: center;
                            gap: 6px;

                            .van-icon {
                                font-size: 14px;
                            }
                        }

                        .course-meta {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            gap: 12px;

                            .course-lessons {
                                font-size: 12px;
                                color: $text-secondary;
                                font-weight: 600;
                                display: flex;
                                align-items: center;
                                gap: 4px;
                                background: rgba(37, 99, 235, 0.08);
                                padding: 4px 8px;
                                border-radius: 8px;

                                .van-icon {
                                    font-size: 12px;
                                    color: $primary-blue;
                                }
                            }

                            .course-requirements {
                                font-size: 11px;
                                color: $text-light;
                                font-weight: 500;
                                display: flex;
                                align-items: center;
                                gap: 4px;

                                .van-icon {
                                    font-size: 11px;
                                    color: #10b981;
                                }
                            }
                        }
                    }
                }
            }
        }

        // 学习动态样式
        .activity-section {
            margin: 0 16px 24px;

            .activity-list {
                background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
                border-radius: 16px;
                overflow: hidden;
                box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08);
                border: 1px solid rgba(37, 99, 235, 0.05);

                .activity-item {
                    ::v-deep .van-cell {
                        padding: 18px 20px;
                        transition: all 0.3s ease;

                        &:not(:last-child) {
                            border-bottom: 1px solid rgba(37, 99, 235, 0.06);
                        }

                        &:active {
                            background-color: rgba(37, 99, 235, 0.02);
                        }

                        .van-cell__title {
                            color: $text-primary;
                            font-weight: 600;
                        }

                        .van-cell__label {
                            color: $text-secondary;
                            font-weight: 500;
                        }

                        .van-cell__value {
                            color: $primary-blue;
                            font-weight: 600;
                            font-size: 12px;
                        }
                    }
                }
            }
        }

        // 通用区块标题样式
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding: 0 8px;

            h3 {
                font-size: 20px;
                font-weight: 700;
                color: $text-primary;
                margin: 0;
                position: relative;

                &::before {
                    content: '';
                    position: absolute;
                    left: -8px;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 4px;
                    height: 20px;
                    background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                    border-radius: 2px;
                }
            }

            .more-btn {
                font-size: 14px;
                color: $primary-blue;
                font-weight: 600;
                cursor: pointer;
                padding: 6px 12px;
                border-radius: 12px;
                background: rgba(37, 99, 235, 0.08);
                transition: all 0.3s ease;

                &:active {
                    background: rgba(37, 99, 235, 0.15);
                    transform: scale(0.95);
                }
            }
        }
    }
}
</style>
