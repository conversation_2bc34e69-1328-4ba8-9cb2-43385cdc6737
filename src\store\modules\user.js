import { getUserInfo, login, logout } from '@/api/user'

const state = {
    token: localStorage.getItem('token') || '',
    userInfo: JSON.parse(localStorage.getItem('userInfo')) || null,
    roles: []
}

const getters = {
    currentUser: state => state.userInfo,
    isLoggedIn: state => !!state.token,
    userRoles: state => state.roles
}

const mutations = {
    SET_TOKEN(state, token) {
        state.token = token
        localStorage.setItem('token', token)
    },
    SET_USER_INFO(state, userInfo) {
        state.userInfo = userInfo
        localStorage.setItem('userInfo', JSON.stringify(userInfo))
    },
    SET_ROLES(state, roles) {
        state.roles = roles
    },
    CLEAR_USER(state) {
        state.token = ''
        state.userInfo = null
        state.roles = []
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
    }
}

const actions = {
    // 登录
    async login({ commit, dispatch }, userData) {
        try {
            commit('SET_LOADING', true)
            const data = await login(userData)
            if (data && data.token) {
                commit('SET_TOKEN', data.token)
                // 获取用户信息
                await dispatch('getUserInfo')
            }
            return data
        } catch (error) {
            console.error('Login failed:', error)
            throw error
        } finally {
            commit('SET_LOADING', false)
        }
    },

    // 获取用户信息
    async getUserInfo({ commit }) {
        try {
            const data = await getUserInfo()
            if (data) {
                commit('SET_USER_INFO', data)
                commit('SET_ROLES', data.roles || [])
            }
            return data
        } catch (error) {
            console.error('Get user info failed:', error)
            // 如果获取用户信息失败，清除登录状态
            commit('CLEAR_USER')
            throw error
        }
    },

    // 退出登录
    async logout({ commit }) {
        try {
            commit('SET_LOADING', true)
            await logout()
        } catch (error) {
            console.error('Logout failed:', error)
        } finally {
            commit('CLEAR_USER')
            commit('SET_LOADING', false)
        }
    }
}

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions
}
