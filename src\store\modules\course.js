import { enrollCourse, getCourseDetail, getCourseList } from '@/api/course'

const state = {
    courseList: [],
    currentCourse: null,
    totalCount: 0,
    pageSize: 10,
    currentPage: 1
}

const getters = {
    courses: state => state.courseList,
    courseDetail: state => state.currentCourse,
    courseTotal: state => state.totalCount
}

const mutations = {
    SET_COURSE_LIST(state, data) {
        state.courseList = data.list || []
        state.totalCount = data.total || 0
        state.currentPage = data.currentPage || 1
        state.pageSize = data.pageSize || 10
    },
    SET_COURSE_DETAIL(state, course) {
        state.currentCourse = course
    },
    CLEAR_COURSE_DETAIL(state) {
        state.currentCourse = null
    }
}

const actions = {
    // 获取课程列表
    async getCourseList({ commit }, params) {
        try {
            commit('SET_LOADING', true)
            const data = await getCourseList(params)
            commit('SET_COURSE_LIST', data)
            return data
        } catch (error) {
            console.error('Get course list failed:', error)
            throw error
        } finally {
            commit('SET_LOADING', false)
        }
    },

    // 获取课程详情
    async getCourseDetail({ commit }, courseId) {
        try {
            commit('SET_LOADING', true)
            const data = await getCourseDetail(courseId)
            commit('SET_COURSE_DETAIL', data)
            return data
        } catch (error) {
            console.error('Get course detail failed:', error)
            throw error
        } finally {
            commit('SET_LOADING', false)
        }
    },

    // 报名课程
    async enrollCourse({ commit }, courseId) {
        try {
            commit('SET_LOADING', true)
            const data = await enrollCourse(courseId)
            return data
        } catch (error) {
            console.error('Enroll course failed:', error)
            throw error
        } finally {
            commit('SET_LOADING', false)
        }
    }
}

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions
}
